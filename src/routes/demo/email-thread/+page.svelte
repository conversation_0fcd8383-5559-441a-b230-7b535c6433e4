<script lang="ts">
	import EmailThread from '$lib/components/conversation/EmailThread.svelte';
	import { onMount } from 'svelte';

	let loading = false;
	let threads: any[] = [];

	// Handle thread toggle events
	function handleThreadToggle(event: CustomEvent) {
		console.log('Thread toggled:', event.detail);
	}

	// Handle email click events
	function handleEmailClick(event: CustomEvent) {
		console.log('Email clicked:', event.detail);
		// Here you could open a detailed email view, mark as read, etc.
	}

	// Handle email action events
	function handleEmailAction(event: CustomEvent) {
		const { action, email } = event.detail;
		console.log(`Email action "${action}" triggered for:`, email);

		// Here you would implement the actual action logic
		switch (action) {
			case 'reply':
				alert(`Reply to email from ${email.sender.name}`);
				break;
			case 'replyAll':
				alert(`Reply All to email from ${email.sender.name}`);
				break;
			case 'forward':
				alert(`Forward email from ${email.sender.name}`);
				break;
			case 'addTask':
				alert(`Create task from email: ${email.subject}`);
				break;
			case 'addToCadence':
				alert(`Add email to cadence: ${email.subject}`);
				break;
			case 'instantBooker':
				alert(`Open instant booker for email: ${email.subject}`);
				break;
			case 'more':
				alert('Show more actions menu');
				break;
			default:
				console.log('Unknown action:', action);
		}
	}



	onMount(() => {
		// Component will use mock data by default
	});
</script>

<svelte:head>
	<title>Email Thread</title>
</svelte:head>

<div class="bg-gray-100 h-screen overflow-hidden">
	<!-- Main content -->
	<div class="h-full">
		<div class="flex h-full">
			<!-- Customer List -->
			<div class="w-1/4 h-full">
				<div class="bg-gray-100 shadow-sm border border-gray-200 p-6 h-full">
					<h3 class="text-lg font-medium text-gray-400">
						CPI
					</h3>
				</div>
			</div>

			<!-- Email Thread Component -->
			<div class="flex-1 h-full">
				<div class="bg-gray-100 shadow-sm border border-gray-200 flex flex-col h-full">
					<div class="px-6 py-6 border-b border-gray-200 flex-shrink-0">
						<h2 class="text-lg font-medium text-gray-400">Header</h2>
					</div>

					<div class="bg-white flex-1 overflow-hidden">
						<EmailThread
							{threads}
							{loading}
							on:threadToggle={handleThreadToggle}
							on:emailClick={handleEmailClick}
							on:emailAction={handleEmailAction}
						/>
					</div>
				</div>
			</div>

			<!-- Info Panel -->
			<div class="w-1/4 h-full">
				<div class="bg-gray-100 shadow-sm border border-gray-200 p-6 h-full">
					<h3 class="text-lg font-medium text-gray-400">
						Customer Info
					</h3>
				</div>
			</div>
		</div>
	</div>
</div>


